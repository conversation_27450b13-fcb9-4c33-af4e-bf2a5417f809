import React, { useState, useEffect } from 'react';
import { Check, Clock, Sparkles } from 'lucide-react';

const steps = [
  { id: 'parsing', label: 'Parsing your prompt', description: 'Understanding project requirements' },
  { id: 'architecture', label: 'Designing architecture', description: 'Creating system architecture' },
  { id: 'frontend', label: 'Generating frontend', description: 'Building user interface components' },
  { id: 'backend', label: 'Creating backend', description: 'Setting up API endpoints and logic' },
  { id: 'database', label: 'Configuring database', description: 'Designing database schema' },
  { id: 'deployment', label: 'Preparing deployment', description: 'Creating deployment configurations' },
  { id: 'testing', label: 'Adding tests', description: 'Generating test suites' },
  { id: 'complete', label: 'Code generation complete', description: 'Your application is ready!' }
];

export const GenerationProgress: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set<number>());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < steps.length - 1) {
          setCompletedSteps(prevCompleted => new Set([...prevCompleted, prev]));
          return prev + 1;
        }
        return prev;
      });
    }, 400);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl mb-4">
            <Sparkles className="h-8 w-8 text-white animate-pulse" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Generating Your Application</h2>
          <p className="text-gray-400">Please wait while we create your full-stack application...</p>
        </div>

        <div className="space-y-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-center space-x-4 p-4 rounded-xl transition-all ${
                index === currentStep
                  ? 'bg-blue-500/20 border border-blue-500/30'
                  : completedSteps.has(index)
                  ? 'bg-green-500/20 border border-green-500/30'
                  : 'bg-black/20 border border-white/10'
              }`}
            >
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                completedSteps.has(index)
                  ? 'bg-green-500'
                  : index === currentStep
                  ? 'bg-blue-500'
                  : 'bg-gray-600'
              }`}>
                {completedSteps.has(index) ? (
                  <Check className="h-5 w-5 text-white" />
                ) : index === currentStep ? (
                  <Clock className="h-5 w-5 text-white animate-spin" />
                ) : (
                  <span className="text-white text-sm font-medium">{index + 1}</span>
                )}
              </div>
              
              <div className="flex-1">
                <h3 className={`font-semibold ${
                  index === currentStep ? 'text-blue-400' : 
                  completedSteps.has(index) ? 'text-green-400' : 'text-gray-400'
                }`}>
                  {step.label}
                </h3>
                <p className="text-sm text-gray-500">{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8">
          <div className="bg-black/20 rounded-full h-2 mb-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
          <div className="text-center text-sm text-gray-400">
            {currentStep + 1} of {steps.length} steps completed
          </div>
        </div>
      </div>
    </div>
  );
};