const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error
  let error = {
    success: false,
    error: 'Internal server error'
  };

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const messages = Object.values(err.errors).map(val => val.message);
    error.error = messages.join(', ');
    return res.status(400).json(error);
  }

  // Mongoose duplicate key error
  if (err.code === 11000) {
    error.error = 'Duplicate field value entered';
    return res.status(400).json(error);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error.error = 'Invalid token';
    return res.status(401).json(error);
  }

  // OpenAI API errors
  if (err.response && err.response.status) {
    if (err.response.status === 401) {
      error.error = 'Invalid API key';
      return res.status(401).json(error);
    }
    if (err.response.status === 429) {
      error.error = 'API rate limit exceeded';
      return res.status(429).json(error);
    }
    if (err.response.status === 402) {
      error.error = 'API quota exceeded';
      return res.status(402).json(error);
    }
  }

  // Custom error messages
  if (err.message) {
    error.error = err.message;
  }

  res.status(err.statusCode || 500).json(error);
};

module.exports = {
  errorHandler
};