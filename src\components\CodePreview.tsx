import React, { useState } from 'react';
import { Copy, Check } from 'lucide-react';

interface CodePreviewProps {
  code: {
    frontend: string;
    backend: string;
    database: string;
    deployment: string;
  };
}

export const CodePreview: React.FC<CodePreviewProps> = ({ code }) => {
  const [activeTab, setActiveTab] = useState<keyof typeof code>('frontend');
  const [copiedFile, setCopiedFile] = useState<string | null>(null);

  const tabs = [
    { id: 'frontend', label: 'Frontend', color: 'from-blue-500 to-cyan-500' },
    { id: 'backend', label: 'Backend', color: 'from-green-500 to-blue-500' },
    { id: 'database', label: 'Database', color: 'from-yellow-500 to-orange-500' },
    { id: 'deployment', label: 'Deployment', color: 'from-purple-500 to-indigo-500' }
  ] as const;

  const copyToClipboard = (text: string, fileName: string) => {
    navigator.clipboard.writeText(text);
    setCopiedFile(fileName);
    setTimeout(() => setCopiedFile(null), 2000);
  };

  return (
    <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Generated Code</h3>
        <div className="text-sm text-gray-400">
          Full-stack application ready for deployment
        </div>
      </div>

      {/* Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${
              activeTab === tab.id
                ? `bg-gradient-to-r ${tab.color} text-white`
                : 'bg-black/20 text-gray-400 hover:text-white hover:bg-black/40'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Code Display */}
      <div className="relative">
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={() => copyToClipboard(code[activeTab], activeTab)}
            className="flex items-center space-x-2 px-3 py-2 bg-black/40 backdrop-blur-sm border border-white/20 rounded-lg text-gray-300 hover:text-white transition-colors"
          >
            {copiedFile === activeTab ? (
              <>
                <Check className="h-4 w-4" />
                <span className="text-sm">Copied!</span>
              </>
            ) : (
              <>
                <Copy className="h-4 w-4" />
                <span className="text-sm">Copy</span>
              </>
            )}
          </button>
        </div>
        
        <div className="bg-black/60 rounded-xl p-6 overflow-x-auto">
          <pre className="text-sm text-gray-300 whitespace-pre-wrap">
            <code>{code[activeTab]}</code>
          </pre>
        </div>
      </div>

      {/* File Structure */}
      <div className="mt-6 p-4 bg-black/20 rounded-xl">
        <h4 className="text-sm font-medium text-gray-400 mb-2">Generated Files:</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          <div className="text-blue-400">📁 frontend/</div>
          <div className="text-green-400">📁 backend/</div>
          <div className="text-yellow-400">📁 database/</div>
          <div className="text-purple-400">📁 deployment/</div>
        </div>
      </div>
    </div>
  );
};