import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting AI Code Generator...');
console.log('📁 Project directory:', __dirname);

// Start the server
console.log('🔧 Starting backend server...');
const server = spawn('node', ['server/app.cjs'], {
  cwd: __dirname,
  stdio: 'inherit'
});

server.on('error', (error) => {
  console.error('❌ Server failed to start:', error.message);
});

server.on('exit', (code) => {
  console.log(`🔴 Server exited with code ${code}`);
});

// Give server time to start
setTimeout(() => {
  console.log('✅ Server should be running on http://localhost:5000');
  console.log('✅ Frontend should be accessible on http://localhost:5173 or http://localhost:5174');
  console.log('');
  console.log('🎯 Next steps:');
  console.log('1. Open your browser to the frontend URL');
  console.log('2. Navigate to the AI Code Generator');
  console.log('3. Fill in the form and click "Generate Code"');
  console.log('4. Both OpenAI and Gemini should work now!');
}, 2000);

// Keep the process running
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  server.kill();
  process.exit(0);
});
