const API_BASE_URL = 'http://localhost:5000/api';

interface GenerateCodeRequest {
  prompt: string;
  stack: string;
  projectType: string;
  provider?: 'openai' | 'gemini';
}

interface GenerateCodeResponse {
  success: boolean;
  data?: {
    code: {
      frontend: string;
      backend: string;
      database: string;
      deployment: string;
    };
    metadata: {
      stack: string;
      projectType: string;
      provider: string;
      generatedAt: string;
      steps: Array<{
        step: string;
        message: string;
        completed: boolean;
      }>;
    };
  };
  error?: string;
}

interface HealthResponse {
  status: string;
  timestamp: string;
  uptime: number;
  environment: string;
  apis: {
    openai: string;
    gemini: string;
  };
}

interface ApiStatusResponse {
  openai: {
    configured: boolean;
    status: string;
  };
  gemini: {
    configured: boolean;
    status: string;
  };
}

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  async generateCode(request: GenerateCodeRequest): Promise<GenerateCodeResponse> {
    return this.request<GenerateCodeResponse>('/generate/code', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getStacks() {
    return this.request('/generate/stacks');
  }

  async getProjectTypes() {
    return this.request('/generate/project-types');
  }

  async getHealth(): Promise<HealthResponse> {
    return this.request<HealthResponse>('/health');
  }

  async getApiStatus(): Promise<ApiStatusResponse> {
    return this.request<ApiStatusResponse>('/health/api-status');
  }
}

export const apiService = new ApiService();
export type { GenerateCodeRequest, GenerateCodeResponse, HealthResponse, ApiStatusResponse };