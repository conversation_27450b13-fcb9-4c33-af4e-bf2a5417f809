import React from 'react';
import { 
  Code, 
  Database, 
  Smartphone, 
  Globe, 
  Shield, 
  Zap, 
  Users, 
  Settings,
  ArrowRight
} from 'lucide-react';

const features = [
  {
    icon: Code,
    title: 'Multi-Stack Support',
    description: 'Generate code for MERN, MEVN, Django, Laravel, and more popular tech stacks.',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: Database,
    title: 'Database Integration',
    description: 'Automatic database schema generation with relationships and migrations.',
    color: 'from-green-500 to-blue-500'
  },
  {
    icon: Smartphone,
    title: 'Cross-Platform Apps',
    description: 'Generate React Native and Flutter applications for iOS and Android.',
    color: 'from-pink-500 to-rose-500'
  },
  {
    icon: Globe,
    title: 'Deployment Ready',
    description: 'Includes Docker configurations, CI/CD pipelines, and cloud deployment scripts.',
    color: 'from-purple-500 to-indigo-500'
  },
  {
    icon: Shield,
    title: 'Security First',
    description: 'Built-in authentication, authorization, and security best practices.',
    color: 'from-orange-500 to-red-500'
  },
  {
    icon: Zap,
    title: 'Performance Optimized',
    description: 'Generated code follows performance best practices and optimization techniques.',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: Users,
    title: 'Team Collaboration',
    description: 'Share projects with team members and collaborate on generated code.',
    color: 'from-teal-500 to-cyan-500'
  },
  {
    icon: Settings,
    title: 'Customizable Output',
    description: 'Fine-tune generated code with custom configurations and preferences.',
    color: 'from-indigo-500 to-purple-500'
  }
];

export const Features: React.FC = () => {
  return (
    <div className="py-20 relative">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-cyan-500/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Everything You Need to Build
            <span className="block bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Production-Ready Apps
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our AI-powered platform generates complete, scalable applications with all the features you need to go from idea to deployment in minutes.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className="group bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:bg-black/60 transition-all duration-300 hover:transform hover:scale-105"
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${feature.color} mb-4 flex items-center justify-center group-hover:scale-110 transition-transform`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                <p className="text-gray-400 text-sm leading-relaxed">{feature.description}</p>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-blue-500/30 rounded-3xl p-8 max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Transform Your Development Process?
            </h3>
            <p className="text-gray-300 mb-6">
              Join thousands of developers who are already using AI to build better applications faster.
            </p>
            <button className="group inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-all transform hover:scale-105">
              <span>Start Building Now</span>
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};