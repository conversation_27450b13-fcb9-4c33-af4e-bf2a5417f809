const express = require('express');
const router = express.Router();

// Health check endpoint
router.get('/', (req, res) => {
  const healthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    apis: {
      openai: process.env.OPENAI_API_KEY ? 'configured' : 'missing',
      gemini: process.env.GEMINI_API_KEY ? 'configured' : 'missing'
    }
  };

  res.json(healthStatus);
});

// API status check
router.get('/api-status', (req, res) => {
  const apiStatus = {
    openai: {
      configured: !!process.env.OPENAI_API_KEY,
      status: process.env.OPENAI_API_KEY ? 'ready' : 'needs_configuration'
    },
    gemini: {
      configured: !!process.env.GEMINI_API_KEY,
      status: process.env.GEMINI_API_KEY ? 'ready' : 'needs_configuration'
    }
  };

  res.json(apiStatus);
});

module.exports = router;