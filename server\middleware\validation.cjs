const validateGenerationRequest = (req, res, next) => {
  const { prompt, stack, projectType } = req.body;

  // Validate required fields
  if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Prompt is required and must be a non-empty string'
    });
  }

  if (!stack || typeof stack !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Stack selection is required'
    });
  }

  if (!projectType || typeof projectType !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Project type selection is required'
    });
  }

  // Validate stack options
  const validStacks = ['mern', 'mevn', 'django', 'nextjs', 'laravel', 'flask'];
  if (!validStacks.includes(stack)) {
    return res.status(400).json({
      success: false,
      error: `Invalid stack. Must be one of: ${validStacks.join(', ')}`
    });
  }

  // Validate project type options
  const validProjectTypes = ['website', 'webapp', 'api', 'mobile'];
  if (!validProjectTypes.includes(projectType)) {
    return res.status(400).json({
      success: false,
      error: `Invalid project type. Must be one of: ${validProjectTypes.join(', ')}`
    });
  }

  // Validate prompt length
  if (prompt.length > 2000) {
    return res.status(400).json({
      success: false,
      error: 'Prompt must be less than 2000 characters'
    });
  }

  next();
};

module.exports = {
  validateGenerationRequest
};