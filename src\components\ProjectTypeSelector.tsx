import React from 'react';
import { Globe, Server, Smartphone, Database, Check } from 'lucide-react';

interface ProjectTypeSelectorProps {
  selectedType: string;
  onTypeSelect: (type: string) => void;
}

const projectTypes = [
  {
    id: 'website',
    name: 'Website',
    description: 'Static or dynamic websites',
    icon: Globe,
    color: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'webapp',
    name: 'Web Application',
    description: 'Full-stack web applications',
    icon: Server,
    color: 'from-green-500 to-blue-500'
  },
  {
    id: 'api',
    name: 'API / Backend',
    description: 'REST APIs and backend services',
    icon: Database,
    color: 'from-purple-500 to-indigo-500'
  },
  {
    id: 'mobile',
    name: 'Mobile App',
    description: 'Cross-platform mobile applications',
    icon: Smartphone,
    color: 'from-pink-500 to-rose-500'
  }
];

export const ProjectTypeSelector: React.FC<ProjectTypeSelectorProps> = ({ selectedType, onTypeSelect }) => {
  return (
    <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Select Project Type</h3>
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {projectTypes.map((type) => {
          const Icon = type.icon;
          return (
            <button
              key={type.id}
              onClick={() => onTypeSelect(type.id)}
              className={`relative p-4 rounded-xl border-2 transition-all text-center ${
                selectedType === type.id
                  ? 'border-blue-500 bg-blue-500/10'
                  : 'border-white/10 hover:border-white/20 bg-black/20 hover:bg-black/40'
              }`}
            >
              {selectedType === type.id && (
                <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
              
              <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${type.color} mb-3 flex items-center justify-center mx-auto`}>
                <Icon className="h-6 w-6 text-white" />
              </div>
              
              <h4 className="font-semibold text-white mb-1">{type.name}</h4>
              <p className="text-xs text-gray-400">{type.description}</p>
            </button>
          );
        })}
      </div>
    </div>
  );
};