const express = require('express');
const router = express.Router();
const { generateCode } = require('../controllers/codeGeneratorController');
const { validateGenerationRequest } = require('../middleware/validation');

// Generate code endpoint
router.post('/code', validateGenerationRequest, generateCode);

// Get supported stacks
router.get('/stacks', (req, res) => {
  const stacks = [
    {
      id: 'mern',
      name: 'MERN Stack',
      description: 'MongoDB, Express.js, React, Node.js',
      technologies: ['React', 'Node.js', 'MongoDB', 'Express.js'],
      color: 'from-green-500 to-blue-500'
    },
    {
      id: 'mevn',
      name: 'MEVN Stack',
      description: 'MongoDB, Express.js, Vue.js, Node.js',
      technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Express.js'],
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'django',
      name: 'Django Stack',
      description: 'Python Django with PostgreSQL',
      technologies: ['Django', 'Python', 'PostgreSQL', 'React'],
      color: 'from-blue-500 to-indigo-500'
    },
    {
      id: 'nextjs',
      name: 'Next.js Stack',
      description: 'Next.js with Prisma and PostgreSQL',
      technologies: ['Next.js', 'Prisma', 'PostgreSQL', 'TypeScript'],
      color: 'from-gray-700 to-gray-900'
    },
    {
      id: 'laravel',
      name: 'Laravel Stack',
      description: 'Laravel with MySQL and Vue.js',
      technologies: ['Laravel', 'PHP', 'MySQL', 'Vue.js'],
      color: 'from-red-500 to-orange-500'
    },
    {
      id: 'flask',
      name: 'Flask Stack',
      description: 'Flask with SQLAlchemy and React',
      technologies: ['Flask', 'Python', 'SQLAlchemy', 'React'],
      color: 'from-blue-600 to-purple-600'
    }
  ];

  res.json({ success: true, stacks });
});

// Get supported project types
router.get('/project-types', (req, res) => {
  const projectTypes = [
    {
      id: 'website',
      name: 'Website',
      description: 'Static or dynamic websites',
      features: ['Responsive Design', 'SEO Optimized', 'Fast Loading']
    },
    {
      id: 'webapp',
      name: 'Web Application',
      description: 'Full-stack web applications',
      features: ['User Authentication', 'Database Integration', 'API Endpoints']
    },
    {
      id: 'api',
      name: 'API / Backend',
      description: 'REST APIs and backend services',
      features: ['RESTful APIs', 'Database Models', 'Authentication']
    },
    {
      id: 'mobile',
      name: 'Mobile App',
      description: 'Cross-platform mobile applications',
      features: ['Cross-Platform', 'Native Performance', 'App Store Ready']
    }
  ];

  res.json({ success: true, projectTypes });
});

module.exports = router;