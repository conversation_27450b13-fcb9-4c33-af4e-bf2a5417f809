const parseGeneratedCode = (generatedContent, stack, projectType) => {
  // This function parses the AI-generated content and structures it
  // In a real implementation, you'd use more sophisticated parsing
  
  const sections = {
    frontend: '',
    backend: '',
    database: '',
    deployment: '',
    package: '',
    environment: '',
    tests: ''
  };

  // Split content by common section markers
  const lines = generatedContent.split('\n');
  let currentSection = 'frontend';
  let codeBlock = false;
  let currentCode = [];

  for (const line of lines) {
    // Detect section headers
    if (line.toLowerCase().includes('frontend') || line.toLowerCase().includes('client')) {
      if (currentCode.length > 0) {
        sections[currentSection] = currentCode.join('\n');
        currentCode = [];
      }
      currentSection = 'frontend';
      continue;
    } else if (line.toLowerCase().includes('backend') || line.toLowerCase().includes('server')) {
      if (currentCode.length > 0) {
        sections[currentSection] = currentCode.join('\n');
        currentCode = [];
      }
      currentSection = 'backend';
      continue;
    } else if (line.toLowerCase().includes('database') || line.toLowerCase().includes('schema')) {
      if (currentCode.length > 0) {
        sections[currentSection] = currentCode.join('\n');
        currentCode = [];
      }
      currentSection = 'database';
      continue;
    } else if (line.toLowerCase().includes('deployment') || line.toLowerCase().includes('docker')) {
      if (currentCode.length > 0) {
        sections[currentSection] = currentCode.join('\n');
        currentCode = [];
      }
      currentSection = 'deployment';
      continue;
    }

    // Detect code blocks
    if (line.includes('```')) {
      codeBlock = !codeBlock;
      continue;
    }

    // Add content to current section
    if (codeBlock || line.trim().length > 0) {
      currentCode.push(line);
    }
  }

  // Add the last section
  if (currentCode.length > 0) {
    sections[currentSection] = currentCode.join('\n');
  }

  // If sections are empty, provide fallback content based on stack
  if (!sections.frontend.trim()) {
    sections.frontend = generateFallbackCode('frontend', stack, projectType);
  }
  if (!sections.backend.trim()) {
    sections.backend = generateFallbackCode('backend', stack, projectType);
  }
  if (!sections.database.trim()) {
    sections.database = generateFallbackCode('database', stack, projectType);
  }
  if (!sections.deployment.trim()) {
    sections.deployment = generateFallbackCode('deployment', stack, projectType);
  }

  return sections;
};

const generateFallbackCode = (section, stack, projectType) => {
  const fallbacks = {
    frontend: {
      mern: `// React Frontend Application
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/dashboard" element={<DashboardPage />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;`,
      nextjs: `// Next.js Application
import { AppProps } from 'next/app';
import { SessionProvider } from 'next-auth/react';
import '../styles/globals.css';

function MyApp({ Component, pageProps: { session, ...pageProps } }: AppProps) {
  return (
    <SessionProvider session={session}>
      <Component {...pageProps} />
    </SessionProvider>
  );
}

export default MyApp;`
    },
    backend: {
      mern: `// Express.js Backend Server
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Database connection
mongoose.connect(process.env.MONGODB_URI);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`,
      django: `# Django Backend Application
from django.contrib import admin
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'users', views.UserViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
    path('api/auth/', include('rest_framework.urls')),
]`
    },
    database: {
      mern: `// MongoDB Schema with Mongoose
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  createdAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

module.exports = { User };`,
      django: `# Django Models
from django.db import models
from django.contrib.auth.models import AbstractUser

class User(AbstractUser):
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    role = models.CharField(max_length=20, choices=[('user', 'User'), ('admin', 'Admin')], default='user')
    created_at = models.DateTimeField(auto_now_add=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']`
    },
    deployment: {
      mern: `# Docker Configuration
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]

# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/myapp
    depends_on:
      - mongo
  
  mongo:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db

volumes:
  mongo-data:`,
      django: `# Django Deployment Configuration
FROM python:3.9

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "myproject.wsgi:application"]`
    }
  };

  const stackFallback = fallbacks[section][stack] || fallbacks[section]['mern'] || `// ${section} code for ${stack} stack`;
  return stackFallback;
};

module.exports = {
  parseGeneratedCode
};