import React, { useState } from 'react';
import { ArrowLeft, Send, <PERSON>rk<PERSON>, Settings, Download, Eye, AlertCircle, CheckCircle } from 'lucide-react';
import { StackSelector } from './StackSelector';
import { ProjectTypeSelector } from './ProjectTypeSelector';
import { GenerationProgress } from './GenerationProgress';
import { CodePreview } from './CodePreview';
import { apiService } from '../services/api';
import type { GenerateCodeResponse } from '../services/api';

interface GeneratorProps {
  onBack: () => void;
}

export const Generator: React.FC<GeneratorProps> = ({ onBack }) => {
  const [step, setStep] = useState<'input' | 'generating' | 'complete' | 'error'>('input');
  const [prompt, setPrompt] = useState('');
  const [selectedStack, setSelectedStack] = useState('');
  const [selectedProjectType, setSelectedProjectType] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<'openai' | 'gemini'>('openai');
  const [generatedCode, setGeneratedCode] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [apiStatus, setApiStatus] = useState<any>(null);

  // Check API status on component mount
  React.useEffect(() => {
    checkApiStatus();
  }, []);

  const checkApiStatus = async () => {
    try {
      const status = await apiService.getApiStatus();
      setApiStatus(status);
    } catch (err) {
      console.error('Failed to check API status:', err);
    }
  };

  const handleGenerate = async () => {
    if (!isFormValid) return;
    
    setStep('generating');
    setError(null);

    try {
      const response = await apiService.generateCode({
        prompt,
        stack: selectedStack,
        projectType: selectedProjectType,
        provider: selectedProvider
      });

      if (response.success && response.data) {
        setGeneratedCode(response.data.code);
        setStep('complete');
      } else {
        throw new Error(response.error || 'Failed to generate code');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setStep('error');
    }
  };

  const isFormValid = prompt.trim() && selectedStack && selectedProjectType && 
    ((selectedProvider === 'openai' && apiStatus?.openai?.configured) || 
     (selectedProvider === 'gemini' && apiStatus?.gemini?.configured));

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Home</span>
          </button>
          
          <div className="flex items-center space-x-2">
            <Sparkles className="h-6 w-6 text-blue-400" />
            <h1 className="text-2xl font-bold text-white">AI Code Generator</h1>
          </div>
        </div>

        {step === 'input' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Input */}
            <div className="space-y-6">
              {/* API Status */}
              {apiStatus && (
                <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">API Configuration</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">OpenAI</span>
                      <div className="flex items-center space-x-2">
                        {apiStatus.openai.configured ? (
                          <CheckCircle className="h-5 w-5 text-green-400" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-400" />
                        )}
                        <span className={`text-sm ${apiStatus.openai.configured ? 'text-green-400' : 'text-red-400'}`}>
                          {apiStatus.openai.status}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Google Gemini</span>
                      <div className="flex items-center space-x-2">
                        {apiStatus.gemini.configured ? (
                          <CheckCircle className="h-5 w-5 text-green-400" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-400" />
                        )}
                        <span className={`text-sm ${apiStatus.gemini.configured ? 'text-green-400' : 'text-red-400'}`}>
                          {apiStatus.gemini.status}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Provider Selection */}
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      AI Provider
                    </label>
                    <div className="flex space-x-4">
                      <button
                        onClick={() => setSelectedProvider('openai')}
                        disabled={!apiStatus.openai.configured}
                        className={`px-4 py-2 rounded-lg transition-all ${
                          selectedProvider === 'openai'
                            ? 'bg-blue-500 text-white'
                            : apiStatus.openai.configured
                            ? 'bg-black/20 text-gray-300 hover:bg-black/40'
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        OpenAI GPT-4
                      </button>
                      <button
                        onClick={() => setSelectedProvider('gemini')}
                        disabled={!apiStatus.gemini.configured}
                        className={`px-4 py-2 rounded-lg transition-all ${
                          selectedProvider === 'gemini'
                            ? 'bg-blue-500 text-white'
                            : apiStatus.gemini.configured
                            ? 'bg-black/20 text-gray-300 hover:bg-black/40'
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        Google Gemini
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Prompt Input */}
              <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Describe your project
                </label>
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Create a hotel booking website with user authentication, admin panel, payment gateway, and booking management system..."
                  className="w-full h-32 bg-black/20 border border-white/10 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>

              {/* Project Type Selection */}
              <ProjectTypeSelector
                selectedType={selectedProjectType}
                onTypeSelect={setSelectedProjectType}
              />

              {/* Stack Selection */}
              <StackSelector
                selectedStack={selectedStack}
                onStackSelect={setSelectedStack}
              />

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={!isFormValid}
                className={`w-full flex items-center justify-center space-x-2 px-6 py-4 rounded-xl font-semibold transition-all ${
                  isFormValid
                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white hover:from-blue-600 hover:to-cyan-600 transform hover:scale-105'
                    : 'bg-gray-600 text-gray-300 cursor-not-allowed'
                }`}
              >
                <Send className="h-5 w-5" />
                <span>Generate Code</span>
              </button>
              
              {!isFormValid && apiStatus && (
                <div className="text-center text-sm text-yellow-400">
                  {!apiStatus.openai.configured && !apiStatus.gemini.configured
                    ? 'Please configure at least one API key to generate code'
                    : 'Please fill in all required fields'}
                </div>
              )}
            </div>

            {/* Right Column - Preview */}
            <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Generated Structure Preview</h3>
              <div className="space-y-3 text-sm text-gray-300">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span>Frontend Application</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span>Backend API Server</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                  <span>Database Schema</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-purple-500 rounded"></div>
                  <span>Deployment Configuration</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span>Testing Suite</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'generating' && (
          <GenerationProgress />
        )}

        {step === 'error' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-red-500/20 backdrop-blur-sm border border-red-500/30 rounded-2xl p-8 text-center">
              <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-4">Generation Failed</h2>
              <p className="text-red-300 mb-6">{error}</p>
              <button
                onClick={() => setStep('input')}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-all"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {step === 'complete' && generatedCode && (
          <div className="space-y-6">
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4">
              <button className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-all">
                <Download className="h-5 w-5" />
                <span>Download Code</span>
              </button>
              <button className="flex items-center space-x-2 px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all">
                <Eye className="h-5 w-5" />
                <span>Live Preview</span>
              </button>
              <button className="flex items-center space-x-2 px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all">
                <Settings className="h-5 w-5" />
                <span>Customize</span>
              </button>
            </div>

            {/* Code Preview */}
            <CodePreview code={generatedCode} />
          </div>
        )}
      </div>
    </div>
  );
};