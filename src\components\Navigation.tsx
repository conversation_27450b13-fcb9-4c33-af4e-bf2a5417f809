import React from 'react';
import { <PERSON>, Zap, Github, Menu, X } from 'lucide-react';

interface NavigationProps {
  currentView: 'home' | 'generator';
  setCurrentView: (view: 'home' | 'generator') => void;
}

export const Navigation: React.FC<NavigationProps> = ({ currentView, setCurrentView }) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);

  return (
    <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
              <Code className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              CodeGen AI
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => setCurrentView('home')}
              className={`px-4 py-2 rounded-lg transition-all ${
                currentView === 'home' 
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30' 
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              Home
            </button>
            <button
              onClick={() => setCurrentView('generator')}
              className={`px-4 py-2 rounded-lg transition-all ${
                currentView === 'generator' 
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30' 
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              Generator
            </button>
            <a href="#" className="text-gray-300 hover:text-white transition-colors">
              Docs
            </a>
            <a href="#" className="text-gray-300 hover:text-white transition-colors">
              Pricing
            </a>
          </div>

          {/* CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="p-2 text-gray-300 hover:text-white transition-colors">
              <Github className="h-5 w-5" />
            </button>
            <button className="px-6 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-all transform hover:scale-105">
              Get Started
            </button>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-gray-300 hover:text-white transition-colors"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-black/40 backdrop-blur-md border-t border-white/10">
          <div className="px-4 py-4 space-y-2">
            <button
              onClick={() => {
                setCurrentView('home');
                setIsMenuOpen(false);
              }}
              className={`w-full text-left px-4 py-2 rounded-lg transition-all ${
                currentView === 'home' 
                  ? 'bg-blue-500/20 text-blue-400' 
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              Home
            </button>
            <button
              onClick={() => {
                setCurrentView('generator');
                setIsMenuOpen(false);
              }}
              className={`w-full text-left px-4 py-2 rounded-lg transition-all ${
                currentView === 'generator' 
                  ? 'bg-blue-500/20 text-blue-400' 
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              Generator
            </button>
            <a href="#" className="block px-4 py-2 text-gray-300 hover:text-white transition-colors">
              Docs
            </a>
            <a href="#" className="block px-4 py-2 text-gray-300 hover:text-white transition-colors">
              Pricing
            </a>
            <button className="w-full mt-4 px-6 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-all">
              Get Started
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};