import React from 'react';
import { Check } from 'lucide-react';

interface StackSelectorProps {
  selectedStack: string;
  onStackSelect: (stack: string) => void;
}

const stacks = [
  {
    id: 'mern',
    name: 'MERN Stack',
    description: 'MongoDB, Express.js, React, Node.js',
    technologies: ['React', 'Node.js', 'MongoDB', 'Express.js'],
    color: 'from-green-500 to-blue-500'
  },
  {
    id: 'mevn',
    name: 'MEVN Stack',
    description: 'MongoDB, Express.js, Vue.js, Node.js',
    technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Express.js'],
    color: 'from-green-500 to-emerald-500'
  },
  {
    id: 'django',
    name: 'Django Stack',
    description: 'Python Django with PostgreSQL',
    technologies: ['Django', 'Python', 'PostgreSQL', 'React'],
    color: 'from-blue-500 to-indigo-500'
  },
  {
    id: 'nextjs',
    name: 'Next.js Stack',
    description: 'Next.js with Prisma and PostgreSQL',
    technologies: ['Next.js', 'Prisma', 'PostgreSQL', 'TypeScript'],
    color: 'from-gray-700 to-gray-900'
  },
  {
    id: 'laravel',
    name: 'Laravel Stack',
    description: 'Laravel with MySQL and Vue.js',
    technologies: ['Laravel', 'PHP', 'MySQL', 'Vue.js'],
    color: 'from-red-500 to-orange-500'
  },
  {
    id: 'flask',
    name: 'Flask Stack',
    description: 'Flask with SQLAlchemy and React',
    technologies: ['Flask', 'Python', 'SQLAlchemy', 'React'],
    color: 'from-blue-600 to-purple-600'
  }
];

export const StackSelector: React.FC<StackSelectorProps> = ({ selectedStack, onStackSelect }) => {
  return (
    <div className="bg-black/40 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Choose Technology Stack</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {stacks.map((stack) => (
          <button
            key={stack.id}
            onClick={() => onStackSelect(stack.id)}
            className={`relative p-4 rounded-xl border-2 transition-all text-left ${
              selectedStack === stack.id
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-white/10 hover:border-white/20 bg-black/20 hover:bg-black/40'
            }`}
          >
            {selectedStack === stack.id && (
              <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
              </div>
            )}
            
            <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${stack.color} mb-3 flex items-center justify-center`}>
              <span className="text-white font-bold text-lg">{stack.name.charAt(0)}</span>
            </div>
            
            <h4 className="font-semibold text-white mb-1">{stack.name}</h4>
            <p className="text-sm text-gray-400 mb-3">{stack.description}</p>
            
            <div className="flex flex-wrap gap-1">
              {stack.technologies.map((tech) => (
                <span
                  key={tech}
                  className="px-2 py-1 bg-white/10 text-xs text-gray-300 rounded-md"
                >
                  {tech}
                </span>
              ))}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};