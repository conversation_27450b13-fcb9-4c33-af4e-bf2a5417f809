# Full-Stack Code Generator Platform

A sophisticated AI-powered platform that generates production-ready full-stack applications from natural language prompts. Built with React, TypeScript, Tailwind CSS, and Node.js with Express.

## 🚀 Features

- **Natural Language Processing**: Convert plain English descriptions into complete applications
- **Multiple Tech Stacks**: Support for MERN, MEVN, Django, Next.js, Laravel, and Flask
- **AI Provider Integration**: Works with both OpenAI GPT-4 and Google Gemini
- **Project Types**: Generate websites, web apps, APIs, and mobile applications
- **Real-time Generation**: Live progress tracking with detailed steps
- **Code Preview**: Syntax-highlighted code preview with copy functionality
- **Production Ready**: Generated code includes authentication, database schemas, and deployment configs

## 🛠️ Tech Stack

### Frontend
- React 18 with TypeScript
- Tailwind CSS for styling
- Lucide React for icons
- Vite for development and building

### Backend
- Node.js with Express.js
- OpenAI API integration
- Google Gemini API integration
- Rate limiting and security middleware
- Comprehensive error handling

## 📋 Prerequisites

- Node.js 18+ installed
- Either an OpenAI API key or Google Gemini API key (or both)

## 🚀 Quick Start

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd code-generator-platform
npm install
```

### 2. Environment Configuration

Create a `.env` file in the root directory:

```bash
cp .env.example .env
```

Edit the `.env` file and add your API keys:

```env
# API Configuration - Add at least one
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 3. Start the Application

**Option 1: Development Mode (Recommended)**
```bash
# Terminal 1 - Start the backend server
npm run server:dev

# Terminal 2 - Start the frontend
npm run dev
```

**Option 2: Production Mode**
```bash
# Start backend
npm run server

# In another terminal, start frontend
npm run dev
```

### 4. Access the Application

- Frontend: http://localhost:5173
- Backend API: http://localhost:5000
- Health Check: http://localhost:5000/api/health

## 🔑 API Key Setup

### OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `.env` file as `OPENAI_API_KEY`

### Google Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GEMINI_API_KEY`

## 📚 API Documentation

### Health Endpoints
- `GET /api/health` - Server health status
- `GET /api/health/api-status` - API configuration status

### Generation Endpoints
- `POST /api/generate/code` - Generate code from prompt
- `GET /api/generate/stacks` - Get supported tech stacks
- `GET /api/generate/project-types` - Get supported project types

### Example Request
```json
POST /api/generate/code
{
  "prompt": "Create a hotel booking website with admin panel",
  "stack": "mern",
  "projectType": "webapp",
  "provider": "openai"
}
```

## 🏗️ Project Structure

```
├── src/                    # Frontend source code
│   ├── components/         # React components
│   ├── services/          # API service layer
│   └── ...
├── server/                # Backend source code
│   ├── controllers/       # Request handlers
│   ├── middleware/        # Express middleware
│   ├── routes/           # API routes
│   ├── utils/            # Utility functions
│   └── app.js            # Express app setup
├── .env.example          # Environment variables template
└── README.md
```

## 🔧 Configuration

### Rate Limiting
The API includes rate limiting to prevent abuse:
- Default: 100 requests per 15 minutes per IP
- Configurable via `RATE_LIMIT_WINDOW_MS` and `RATE_LIMIT_MAX_REQUESTS`

### CORS
CORS is configured to allow requests from the frontend URL specified in `FRONTEND_URL`.

### Security
- Helmet.js for security headers
- Input validation and sanitization
- Error handling without sensitive information exposure

## 🚀 Deployment

### Backend Deployment
1. Set environment variables on your hosting platform
2. Install dependencies: `npm install`
3. Start the server: `npm run server`

### Frontend Deployment
1. Build the frontend: `npm run build`
2. Deploy the `dist` folder to your static hosting service

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues:

1. Check that your API keys are correctly configured
2. Ensure the backend server is running on port 5000
3. Check the browser console and server logs for error messages
4. Verify your API key has sufficient quota/credits

## 🔮 Future Enhancements

- [ ] User authentication and project saving
- [ ] More AI providers (Claude, Cohere, etc.)
- [ ] Advanced code customization options
- [ ] Real-time collaboration features
- [ ] Code deployment integration
- [ ] Template marketplace
- [ ] Version control integration