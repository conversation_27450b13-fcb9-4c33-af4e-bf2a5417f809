const { OpenAI } = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { generatePrompt } = require('../utils/promptGenerator.cjs');
const { parseGeneratedCode } = require('../utils/codeParser.cjs');

// Initialize AI clients
let openai = null;
let genAI = null;

// Initialize OpenAI if API key is available
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
}

// Initialize Gemini if API key is available
if (process.env.GEMINI_API_KEY) {
  genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
}

const generateCode = async (req, res) => {
  try {
    const { prompt, stack, projectType, provider = 'openai' } = req.body;

    // Generate the system prompt based on user input
    const systemPrompt = generatePrompt(prompt, stack, projectType);

    let generatedContent = '';
    let usedProvider = provider;
    let failoverAttempted = false;

    // Function to try OpenAI
    const tryOpenAI = async () => {
      if (!openai) {
        throw new Error('OpenAI API key not configured');
      }

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.7,
      });

      return completion.choices[0].message.content;
    };

    // Function to try Gemini
    const tryGemini = async () => {
      if (!genAI) {
        throw new Error('Gemini API key not configured');
      }

      const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
      const result = await model.generateContent([
        systemPrompt,
        prompt
      ]);

      const response = await result.response;
      return response.text();
    };

    // Primary API attempt
    try {
      if (provider === 'openai') {
        generatedContent = await tryOpenAI();
      } else if (provider === 'gemini') {
        generatedContent = await tryGemini();
      } else {
        throw new Error('Invalid provider specified');
      }
    } catch (primaryError) {
      console.warn(`Primary API (${provider}) failed:`, primaryError.message);

      // Attempt failover to backup API
      try {
        if (provider === 'openai' && genAI) {
          console.log('Attempting failover to Gemini...');
          generatedContent = await tryGemini();
          usedProvider = 'gemini';
          failoverAttempted = true;
        } else if (provider === 'gemini' && openai) {
          console.log('Attempting failover to OpenAI...');
          generatedContent = await tryOpenAI();
          usedProvider = 'openai';
          failoverAttempted = true;
        } else {
          // No backup available
          throw primaryError;
        }
      } catch (backupError) {
        console.error('Backup API also failed:', backupError.message);
        throw new Error(`Both APIs failed. Primary (${provider}): ${primaryError.message}. Backup: ${backupError.message}`);
      }
    }

    // Parse the generated content into structured code
    const parsedCode = parseGeneratedCode(generatedContent, stack, projectType);

    // Simulate generation steps for frontend
    const generationSteps = [
      { step: 'parsing', message: 'Analyzing your requirements...', completed: true },
      { step: 'architecture', message: 'Designing system architecture...', completed: true },
      { step: 'frontend', message: 'Generating frontend components...', completed: true },
      { step: 'backend', message: 'Creating backend APIs...', completed: true },
      { step: 'database', message: 'Setting up database schema...', completed: true },
      { step: 'deployment', message: 'Preparing deployment configs...', completed: true },
      { step: 'testing', message: 'Adding test suites...', completed: true },
      { step: 'complete', message: 'Code generation complete!', completed: true }
    ];

    res.json({
      success: true,
      data: {
        code: parsedCode,
        metadata: {
          stack,
          projectType,
          provider: usedProvider,
          requestedProvider: provider,
          failoverUsed: failoverAttempted,
          generatedAt: new Date().toISOString(),
          steps: generationSteps
        }
      }
    });

  } catch (error) {
    console.error('Code generation error:', error);
    
    let errorMessage = 'An error occurred during code generation.';
    
    if (error.code === 'insufficient_quota') {
      errorMessage = 'API quota exceeded. Please check your API usage limits.';
    } else if (error.code === 'invalid_api_key') {
      errorMessage = 'Invalid API key. Please check your API configuration.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
};

module.exports = {
  generateCode
};