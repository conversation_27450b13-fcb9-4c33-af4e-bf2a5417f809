const { OpenAI } = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { generatePrompt } = require('../utils/promptGenerator');
const { parseGeneratedCode } = require('../utils/codeParser');

// Initialize AI clients
let openai = null;
let genAI = null;

// Initialize OpenAI if API key is available
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
}

// Initialize Gemini if API key is available
if (process.env.GEMINI_API_KEY) {
  genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
}

const generateCode = async (req, res) => {
  try {
    const { prompt, stack, projectType, provider = 'openai' } = req.body;

    // Check if the selected provider is configured
    if (provider === 'openai' && !openai) {
      return res.status(400).json({
        success: false,
        error: 'OpenAI API key not configured. Please add OPENAI_API_KEY to your environment variables.'
      });
    }

    if (provider === 'gemini' && !genAI) {
      return res.status(400).json({
        success: false,
        error: 'Gemini API key not configured. Please add GEMINI_API_KEY to your environment variables.'
      });
    }

    // Generate the system prompt based on user input
    const systemPrompt = generatePrompt(prompt, stack, projectType);

    let generatedContent = '';

    if (provider === 'openai') {
      // Use OpenAI GPT-4
      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.7,
      });

      generatedContent = completion.choices[0].message.content;
    } else if (provider === 'gemini') {
      // Use Google Gemini
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const result = await model.generateContent([
        systemPrompt,
        prompt
      ]);

      const response = await result.response;
      generatedContent = response.text();
    }

    // Parse the generated content into structured code
    const parsedCode = parseGeneratedCode(generatedContent, stack, projectType);

    // Simulate generation steps for frontend
    const generationSteps = [
      { step: 'parsing', message: 'Analyzing your requirements...', completed: true },
      { step: 'architecture', message: 'Designing system architecture...', completed: true },
      { step: 'frontend', message: 'Generating frontend components...', completed: true },
      { step: 'backend', message: 'Creating backend APIs...', completed: true },
      { step: 'database', message: 'Setting up database schema...', completed: true },
      { step: 'deployment', message: 'Preparing deployment configs...', completed: true },
      { step: 'testing', message: 'Adding test suites...', completed: true },
      { step: 'complete', message: 'Code generation complete!', completed: true }
    ];

    res.json({
      success: true,
      data: {
        code: parsedCode,
        metadata: {
          stack,
          projectType,
          provider,
          generatedAt: new Date().toISOString(),
          steps: generationSteps
        }
      }
    });

  } catch (error) {
    console.error('Code generation error:', error);
    
    let errorMessage = 'An error occurred during code generation.';
    
    if (error.code === 'insufficient_quota') {
      errorMessage = 'API quota exceeded. Please check your API usage limits.';
    } else if (error.code === 'invalid_api_key') {
      errorMessage = 'Invalid API key. Please check your API configuration.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
};

module.exports = {
  generateCode
};