import React, { useState } from 'react';
import { Navigation } from './components/Navigation';
import { Hero } from './components/Hero';
import { Generator } from './components/Generator';
import { Features } from './components/Features';
import { Footer } from './components/Footer';

function App() {
  const [currentView, setCurrentView] = useState<'home' | 'generator'>('home');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      <Navigation currentView={currentView} setCurrentView={setCurrentView} />
      
      {currentView === 'home' ? (
        <>
          <Hero onGetStarted={() => setCurrentView('generator')} />
          <Features />
        </>
      ) : (
        <Generator onBack={() => setCurrentView('home')} />
      )}
      
      <Footer />
    </div>
  );
}

export default App;