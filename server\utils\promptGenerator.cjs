const generatePrompt = (userPrompt, stack, projectType) => {
  const stackConfigs = {
    mern: {
      frontend: 'React with modern hooks and functional components',
      backend: 'Node.js with Express.js',
      database: 'MongoDB with Mongoose ODM',
      styling: 'Tailwind CSS or styled-components'
    },
    mevn: {
      frontend: 'Vue.js 3 with Composition API',
      backend: 'Node.js with Express.js',
      database: 'MongoDB with Mongoose ODM',
      styling: 'Tailwind CSS or Vue-specific styling'
    },
    django: {
      frontend: 'React or Django templates',
      backend: 'Python Django with Django REST Framework',
      database: 'PostgreSQL with Django ORM',
      styling: 'Tailwind CSS or Bootstrap'
    },
    nextjs: {
      frontend: 'Next.js with TypeScript',
      backend: 'Next.js API routes or separate Node.js backend',
      database: 'PostgreSQL with Prisma ORM',
      styling: 'Tailwind CSS with modern design patterns'
    },
    laravel: {
      frontend: 'Vue.js or Laravel Blade templates',
      backend: 'PHP Laravel with Eloquent ORM',
      database: 'MySQL with Laravel migrations',
      styling: 'Tailwind CSS or Bootstrap'
    },
    flask: {
      frontend: 'React or Flask templates with Jinja2',
      backend: 'Python Flask with Flask-RESTful',
      database: 'PostgreSQL with SQLAlchemy ORM',
      styling: 'Tailwind CSS or Bootstrap'
    }
  };

  const projectTypeInstructions = {
    website: 'Focus on creating a responsive, SEO-optimized website with modern design patterns.',
    webapp: 'Create a full-stack web application with user authentication, CRUD operations, and proper state management.',
    api: 'Generate a robust REST API with proper endpoints, authentication, validation, and documentation.',
    mobile: 'Create a cross-platform mobile application with native-like performance and user experience.'
  };

  const selectedStack = stackConfigs[stack] || stackConfigs.mern;
  const projectInstruction = projectTypeInstructions[projectType] || projectTypeInstructions.webapp;

  return `You are an expert full-stack developer. Generate production-ready code based on the following specifications:

PROJECT TYPE: ${projectType}
TECH STACK: ${stack.toUpperCase()}
- Frontend: ${selectedStack.frontend}
- Backend: ${selectedStack.backend}
- Database: ${selectedStack.database}
- Styling: ${selectedStack.styling}

INSTRUCTIONS:
${projectInstruction}

REQUIREMENTS:
1. Generate complete, functional code for all components
2. Include proper error handling and validation
3. Add authentication and authorization where needed
4. Follow best practices and modern coding standards
5. Include database models/schemas with relationships
6. Add proper API documentation and comments
7. Include deployment configurations (Docker, environment setup)
8. Generate test files for critical functionality

OUTPUT FORMAT:
Please structure your response with clear sections for:
- Frontend code (components, pages, utilities)
- Backend code (routes, controllers, models, middleware)
- Database schema and migrations
- Deployment configurations
- Package.json dependencies
- Environment configuration
- Basic test files

USER REQUEST: ${userPrompt}

Generate comprehensive, production-ready code that fulfills this request using the specified technology stack.`;
};

module.exports = {
  generatePrompt
};